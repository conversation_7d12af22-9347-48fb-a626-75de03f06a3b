package com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request;

import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

/**
 * 获取物料开票信息分页请求
 */
@Getter
@Setter
@ToString
public class InvoiceGoodsPageRequest extends ErpPageRequest<InvoiceGoodsResponse> {

    /**
     * 物料代码
     */
    private String itemCode;
    /**
     * 物料描述
     */
    private String description;
    /**
     * 主要单位代码
     */
    private String primaryUomCode;
    /**
     * 开票描述
     */
    private String invoiceDesc;
    /**
     * 规格型号
     */
    private String specification;
    /**
     * 软件描述
     */
    private String softwareDesc;
    /**
     * 默认税率
     */
    private String defaultTaxRate;
    /**
     * 最后更新日期（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String lastUpdateDate;

    @Override
    public String getApiPath() {
        return "/api/erpQuery/getItemInvoiceInfo";
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.GET;
    }

    @Override
    public Class<InvoiceGoodsResponse> getResponseClass() {
        return InvoiceGoodsResponse.class;
    }

}
