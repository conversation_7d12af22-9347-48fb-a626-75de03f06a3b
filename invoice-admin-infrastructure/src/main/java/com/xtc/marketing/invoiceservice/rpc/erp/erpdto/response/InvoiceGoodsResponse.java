package com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 物料开票信息响应
 */
@Getter
@Setter
@ToString
public class InvoiceGoodsResponse {

    /**
     * 物料代码
     */
    @SerializedName(value = "itemCode", alternate = "ITEM_CODE")
    private String itemCode;
    /**
     * 物料描述
     */
    @SerializedName(value = "description", alternate = "DESCRIPTION")
    private String description;
    /**
     * 主要单位代码
     */
    @SerializedName(value = "primaryUomCode", alternate = "PRIMARY_UOM_CODE")
    private String primaryUomCode;
    /**
     * 开票描述
     */
    @SerializedName(value = "invoiceDesc", alternate = "INVOICE_DESC")
    private String invoiceDesc;
    /**
     * 规格型号
     */
    @SerializedName(value = "specification", alternate = "SPECIFICATION")
    private String specification;
    /**
     * 软件描述
     */
    @SerializedName(value = "softwareDesc", alternate = "SOFTWARE_DESC")
    private String softwareDesc;
    /**
     * 默认税率
     */
    @SerializedName(value = "defaultTaxRate", alternate = "DEFAULT_TAX_RATE")
    private BigDecimal defaultTaxRate;
    /**
     * 最后更新日期
     */
    @SerializedName(value = "lastUpdateDate", alternate = "LAST_UPDATE_DATE")
    private String lastUpdateDate;

}
