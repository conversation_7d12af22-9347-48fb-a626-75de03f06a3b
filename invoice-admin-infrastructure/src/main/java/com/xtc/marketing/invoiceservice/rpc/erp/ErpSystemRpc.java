package com.xtc.marketing.invoiceservice.rpc.erp;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.google.gson.annotations.SerializedName;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpBaseRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpBaseResponse;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpMultiResponse;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpSingleResponse;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request.InvoiceGoodsPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import com.xtc.marketing.invoiceservice.util.HttpUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * ERP系统RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ErpSystemRpc {

    /* 配置 */
    /**
     * 生产配置：域名
     */
    private static final String DOMAIN = "http://esb.bbkedu.com:8000";
    /**
     * 生产配置：客户端id
     */
    private static final String CLIENT_ID = "ITMarketing";
    /**
     * 生产配置：客户端密钥
     */
    private static final String CLIENT_SECRET = "6d74932538844e719aef23a5b0ec6176";
    /**
     * 测试配置：域名
     */
    private static final String TEST_DOMAIN = "http://172.28.206.49:8000";
    /**
     * 测试配置：客户端id
     */
    private static final String TEST_CLIENT_ID = "supplychain";
    /**
     * 测试配置：客户端密钥
     */
    private static final String TEST_CLIENT_SECRET = "3644e890f82f4f5e8d7b02419027a6e1";

    /* API 列表 */
    /**
     * API：获取访问令牌
     */
    private static final String API_GET_TOKEN = "/connect/token";

    /* 变量 */
    /**
     * http 请求类
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();
    /**
     * 访问令牌缓存，过期时间1小时
     */
    private final Cache<@NonNull String, @NonNull String> accessTokenCache = CacheBuilder.newBuilder()
            .expireAfterWrite(12, TimeUnit.HOURS).maximumSize(1).build();

    /**
     * 当前环境
     */
    @Value("${spring.profiles.active}")
    private String activeProfile;

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间，单位：毫秒
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(5000);
        requestFactory.setReadTimeout(20000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    private String getAccessToken() {
        try {
            return accessTokenCache.get("accessToken", () -> {
                MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
                form.put("scope", Collections.singletonList("ApiGateway"));
                form.put("grant_type", Collections.singletonList("client_credentials"));
                form.put("client_id", Collections.singletonList(this.isTestEnvironment() ? TEST_CLIENT_ID : CLIENT_ID));
                form.put("client_secret", Collections.singletonList(this.isTestEnvironment() ? TEST_CLIENT_SECRET : CLIENT_SECRET));
                String response = HttpUtil.postForForm(this.getDomain() + API_GET_TOKEN, form);
                return GsonUtil.getAsString(response, "access_token");
            });
        } catch (Exception e) {
            throw rpcSysException("获取访问令牌失败", e);
        }
    }

    /**
     * 获取物料开票信息分页列表
     *
     * @param request 请求
     * @return 物料开票信息分页列表
     */
    public ErpMultiResponse<InvoiceGoodsResponse> pageInvoiceGoods(InvoiceGoodsPageRequest request) {
        return this.call(request);
    }

    /**
     * 执行远程调用
     *
     * @param request 请求
     * @param <T>     响应数据类型
     * @return 响应结果
     */
    private <T, R extends ErpBaseResponse<?>> R call(ErpBaseRequest<T> request) {
        // 构建请求实体
        RequestEntity<?> requestEntity = this.initRequestEntity(request);
        // 日志记录
        String responseStr = "";
        String requestInfoLog = String.format("ERP系统RPC %s url: %s, body: %s",
                requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getBody());
        log.info(requestInfoLog);
        try {
            // 发起请求
            ResponseEntity<String> response = REST_TEMPLATE.exchange(requestEntity, String.class);
            // 去除响应结果中的换行符
            responseStr = this.removeNewline(response.getBody());
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应结果
            R result = this.paresResponse(request, responseStr);
            // 响应失败抛出异常
            if (result == null || result.isFailure()) {
                throw this.remoteException();
            }
            return result;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException httpStatusCodeException) {
                responseStr = httpStatusCodeException.getResponseBodyAsString();
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 解析响应结果
     *
     * @param request     请求
     * @param responseStr 响应结果字符串
     * @param <T>         响应数据类型
     * @return 响应结果
     */
    private <T, R extends ErpBaseResponse<?>> R paresResponse(ErpBaseRequest<T> request, String responseStr) {
        // 当响应数据类型为 Void 时，表示丢弃响应数据并返回 null，保证方法返回值的正确性
        if (request.getResponseClass() == Void.class) {
            return GsonUtil.jsonToBean(responseStr, ErpSingleResponse.class, Void.class);
        }
        ErpSingleResponse<JsonElement> baseResponse = GsonUtil.jsonToBean(responseStr, ErpSingleResponse.class, JsonElement.class);
        if (baseResponse.getData() != null && baseResponse.getData().isJsonArray()) {
            // 针对数组数据，需要返回的 T 类型当作是 List<T> 的一个整体，需要构建 List 类型的 Type
            return GsonUtil.jsonToBean(responseStr, ErpMultiResponse.class, request.getResponseClass());
        }
        // 默认直接返回 T 类型，没有外层嵌套
        return GsonUtil.jsonToBean(responseStr, ErpSingleResponse.class, request.getResponseClass());
    }

    /**
     * 初始化请求
     *
     * @param request 请求
     * @return 请求实体
     */
    private RequestEntity<?> initRequestEntity(ErpBaseRequest<?> request) {
        // 构建请求 url
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(this.getDomain() + request.getApiPath());
        // GET 请求将属性的名称作为请求参数 key，属性的 getXxx 方法作为请求参数 value
        if (request.getRequestMethod() == HttpMethod.GET) {
            Map<String, Object> fieldMap = this.getFieldMap(request,
                    errorFields -> log.warn("ERP系统调用异常，组装失败的请求参数 [{}]", errorFields));
            fieldMap.forEach(uriBuilder::queryParam);
        }
        URI uri = uriBuilder.build().toUri();
        // 构建请求实体
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uri);
        // 添加 header 访问令牌
        requestBuilder.header("Authorization", "Bearer " + this.getAccessToken());
        // POST、PUT 请求设置 json 类型的 body 参数
        if (request.getRequestMethod() == HttpMethod.POST || request.getRequestMethod() == HttpMethod.PUT) {
            return requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(request));
        }
        return requestBuilder.build();
    }

    /**
     * 获取对象所有属性的名称和值的集合
     *
     * @param object      对象
     * @param errorFields 异常处理
     * @return 属性的名称和值的集合
     */
    private Map<String, Object> getFieldMap(Object object, Consumer<String> errorFields) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(object.getClass().getDeclaredFields().length);
        StringJoiner errorJoiner = new StringJoiner(",");
        // 遍历对象的所有属性，以及父类的属性
        Stream<Field> stream = Arrays.stream(object.getClass().getDeclaredFields());
        Class<?> superclass = object.getClass().getSuperclass();
        if (superclass != null && superclass != ErpBaseRequest.class) {
            stream = Stream.concat(stream, Arrays.stream(superclass.getDeclaredFields()));
        }
        // 获取属性的名称和值的集合
        stream.forEach(field -> {
            // key：属性名称，value：属性 getXxx 方法的返回值
            String fieldName = field.getName();
            try {
                // 获取属性的 getXxx 方法并执行
                String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                Method method = object.getClass().getMethod(methodName);
                Object fieldValue = method.invoke(object);
                if (fieldValue != null) {
                    // 如果存在注解 SerializedName 则使用注解中的名称作为查询参数名称
                    String queryName = field.isAnnotationPresent(SerializedName.class)
                            ? field.getAnnotation(SerializedName.class).value() : fieldName;
                    map.put(queryName, fieldValue);
                }
            } catch (Exception e) {
                // 收集异常的属性名称
                errorJoiner.add(fieldName);
            }
        });
        if (errorJoiner.length() > 0) {
            errorFields.accept(errorJoiner.toString());
        }
        return map;
    }

    /**
     * 去除换行符
     *
     * @param str 字符串
     * @return 去除换行符后的字符串
     */
    private String removeNewline(String str) {
        if (str == null) {
            return null;
        }
        return str.replaceAll("[\n\r]", "");
    }

    /**
     * 获取当前环境的域名
     *
     * @return 域名
     */
    private String getDomain() {
        // 根据当前环境返回对应的域名
        return this.isTestEnvironment() ? TEST_DOMAIN : DOMAIN;
    }

    /**
     * 判断当前为测试环境
     *
     * @return 执行结果
     */
    private boolean isTestEnvironment() {
        return "dev,test".contains(activeProfile);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("ERP系统调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, msg, e);
    }

    public static void main(String[] args) {
        ErpSystemRpc rpc = new ErpSystemRpc();
        // 设置当前环境：dev, prod
        rpc.activeProfile = "dev";
        // 测试物料开票信息查询
        InvoiceGoodsPageRequest invoiceGoodsRequest = new InvoiceGoodsPageRequest();
        invoiceGoodsRequest.setCurrentPage(1);
        invoiceGoodsRequest.setPageSize(10);
        rpc.pageInvoiceGoods(invoiceGoodsRequest);
    }

}
