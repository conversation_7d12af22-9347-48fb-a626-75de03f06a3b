package com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询库存
 */
@Getter
@Setter
@ToString
public class InventoryResponse {

    /**
     * 组织
     */
    @SerializedName(value = "organization", alternate = "ORGANIZATION")
    private String organization;
    /**
     * 子库存
     */
    @SerializedName(value = "subInventory", alternate = "SUBINVENTORY")
    private String subInventory;
    /**
     * 储位
     */
    @SerializedName(value = "storagePlace", alternate = "STORAGEPLACE")
    private String storagePlace;
    /**
     * 物料
     */
    @SerializedName(value = "productId", alternate = "PRODUCTID")
    private String productId;
    /**
     * 描述
     */
    @SerializedName(value = "shortName", alternate = "SHORTNAME")
    private String shortName;
    /**
     * 数量
     */
    @SerializedName(value = "num", alternate = "NUM")
    private Integer num;

}
