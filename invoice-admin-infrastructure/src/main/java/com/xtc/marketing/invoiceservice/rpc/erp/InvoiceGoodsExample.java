package com.xtc.marketing.invoiceservice.rpc.erp;

import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpMultiResponse;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request.InvoiceGoodsPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;

import java.util.List;

/**
 * 物料开票信息接口使用示例
 */
public class InvoiceGoodsExample {

    public static void main(String[] args) {
        // 创建ERP系统RPC实例
        ErpSystemRpc erpSystemRpc = new ErpSystemRpc();
        
        // 示例1: 基本分页查询
        System.out.println("=== 示例1: 基本分页查询 ===");
        InvoiceGoodsPageRequest basicRequest = new InvoiceGoodsPageRequest();
        basicRequest.setCurrentPage(1);
        basicRequest.setPageSize(10);
        
        ErpMultiResponse<InvoiceGoodsResponse> basicResponse = erpSystemRpc.getInvoiceGoodsPage(basicRequest);
        printResponse("基本分页查询", basicResponse);
        
        // 示例2: 根据物料代码查询
        System.out.println("\n=== 示例2: 根据物料代码查询 ===");
        InvoiceGoodsPageRequest itemCodeRequest = new InvoiceGoodsPageRequest();
        itemCodeRequest.setCurrentPage(1);
        itemCodeRequest.setPageSize(5);
        itemCodeRequest.setItemCode("5560127");
        
        ErpMultiResponse<InvoiceGoodsResponse> itemCodeResponse = erpSystemRpc.getInvoiceGoodsPage(itemCodeRequest);
        printResponse("物料代码查询", itemCodeResponse);
        
        // 示例3: 根据物料描述模糊查询
        System.out.println("\n=== 示例3: 根据物料描述模糊查询 ===");
        InvoiceGoodsPageRequest descRequest = new InvoiceGoodsPageRequest();
        descRequest.setCurrentPage(1);
        descRequest.setPageSize(5);
        descRequest.setDescription("家教机");
        
        ErpMultiResponse<InvoiceGoodsResponse> descResponse = erpSystemRpc.getInvoiceGoodsPage(descRequest);
        printResponse("物料描述查询", descResponse);
        
        // 示例4: 组合条件查询
        System.out.println("\n=== 示例4: 组合条件查询 ===");
        InvoiceGoodsPageRequest combinedRequest = new InvoiceGoodsPageRequest();
        combinedRequest.setCurrentPage(1);
        combinedRequest.setPageSize(10);
        combinedRequest.setDescription("家教机");
        combinedRequest.setSpecification("S1");
        combinedRequest.setDefaultTaxRate("13");
        
        ErpMultiResponse<InvoiceGoodsResponse> combinedResponse = erpSystemRpc.getInvoiceGoodsPage(combinedRequest);
        printResponse("组合条件查询", combinedResponse);
    }
    
    /**
     * 打印响应结果
     */
    private static void printResponse(String title, ErpMultiResponse<InvoiceGoodsResponse> response) {
        System.out.println(title + " 结果:");
        System.out.println("响应状态: " + response.getStatus());
        System.out.println("响应消息: " + response.getMsg());
        
        if (response.isSuccess()) {
            System.out.println("总页数: " + response.getTotalPages());
            System.out.println("总记录数: " + response.getTotalRows());
            System.out.println("当前页: " + response.getCurrentPage());
            System.out.println("每页数量: " + response.getPageSize());
            
            List<InvoiceGoodsResponse> dataList = response.getData();
            if (dataList != null && !dataList.isEmpty()) {
                System.out.println("数据列表 (共" + dataList.size() + "条):");
                for (int i = 0; i < Math.min(3, dataList.size()); i++) {
                    InvoiceGoodsResponse item = dataList.get(i);
                    System.out.println("  [" + (i + 1) + "] 物料代码: " + item.getItemCode());
                    System.out.println("      物料描述: " + item.getDescription());
                    System.out.println("      开票描述: " + item.getInvoiceDesc());
                    System.out.println("      规格型号: " + item.getSpecification());
                    System.out.println("      单位代码: " + item.getPrimaryUomCode());
                    System.out.println("      默认税率: " + item.getDefaultTaxRate());
                    System.out.println("      更新时间: " + item.getLastUpdateDate());
                    System.out.println();
                }
                if (dataList.size() > 3) {
                    System.out.println("  ... 还有 " + (dataList.size() - 3) + " 条记录");
                }
            } else {
                System.out.println("无数据");
            }
        } else {
            System.err.println("接口调用失败: " + response.getMsg());
        }
        System.out.println("----------------------------------------");
    }
}
