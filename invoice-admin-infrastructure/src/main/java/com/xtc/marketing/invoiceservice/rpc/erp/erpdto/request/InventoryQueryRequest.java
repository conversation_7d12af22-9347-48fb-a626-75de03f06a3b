package com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request;

import com.xtc.onlineretailers.refactor.rpc.erp.erpdto.ErpBaseRequest;
import com.xtc.onlineretailers.refactor.rpc.erp.erpdto.response.InventoryResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.http.HttpMethod;

/**
 * 库存查询命令
 */
@Getter
@Setter
@ToString
public class InventoryQueryRequest extends ErpBaseRequest<InventoryResponse> {

    /**
     * 组织
     */
    private String organization;
    /**
     * 子库存
     */
    private String subInventory;
    /**
     * 储位
     */
    private String storagePlace;
    /**
     * 物料代码
     */
    private String productId;
    /**
     * 物料描述
     */
    private String shortNameFuzzy;

    @Override
    public String getApiPath() {
        return "/api/erpQuery/getEcOnhand";
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.GET;
    }

    @Override
    public Class<InventoryResponse> getResponseClass() {
        return InventoryResponse.class;
    }

}