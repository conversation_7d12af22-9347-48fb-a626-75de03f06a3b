# ERP物料开票信息接口文档

## 接口概述

本接口用于从ERP系统获取物料开票信息，支持分页查询和多种查询条件。

## 实现文件

本次实现包含以下文件：

1. **InvoiceGoodsPageRequest.java** - 请求DTO类
2. **InvoiceGoodsResponse.java** - 响应DTO类
3. **ErpSystemRpc.java** - 添加了新的接口方法 `getInvoiceGoodsPage()`
4. **InvoiceGoodsExample.java** - 使用示例代码
5. **ErpSystemRpcTest.java** - 单元测试类

## 接口信息

- **接口名称**: 获取物料开票信息
- **接口地址**: `/api/erpQuery/getItemInvoiceInfo`
- **请求方式**: GET
- **接口类型**: 分页查询

## 请求参数

### InvoiceGoodsPageRequest

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| currentPage | Integer | 否 | 当前页码 | 1 |
| pageSize | Integer | 否 | 每页数量 | 10 |
| itemCode | String | 否 | 物料代码 | 5560127 |
| description | String | 否 | 物料描述 | 家教机 S1 含电池 4# |
| primaryUomCode | String | 否 | 主要单位代码 | PCS |
| invoiceDesc | String | 否 | 开票描述 | 家教机 |
| specification | String | 否 | 规格型号 | S1 |
| softwareDesc | String | 否 | 软件描述 | null |
| defaultTaxRate | String | 否 | 默认税率 | 13 |
| lastUpdateDate | String | 否 | 最后更新日期 | 2023-03-23 08:29:05 |

## 响应参数

### ErpMultiResponse<InvoiceGoodsResponse>

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| status | String | 响应状态码 | S |
| msg | String | 响应消息 | 成功 |
| totalPages | Integer | 总页数 | null |
| totalRows | Integer | 总记录数 | null |
| currentPage | Integer | 当前页码 | 1 |
| pageSize | Integer | 每页数量 | 10 |
| data | List<InvoiceGoodsResponse> | 数据列表 | - |

### InvoiceGoodsResponse

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| itemCode | String | 物料代码 | 5560127 |
| description | String | 物料描述 | 家教机 S1 含电池 4# |
| primaryUomCode | String | 主要单位代码 | PCS |
| invoiceDesc | String | 开票描述 | 家教机 |
| specification | String | 规格型号 | S1 |
| softwareDesc | String | 软件描述 | null |
| defaultTaxRate | String | 默认税率 | 13 |
| lastUpdateDate | String | 最后更新日期 | 2023-03-23 08:29:05 |

## 使用示例

```java
// 创建ERP系统RPC实例
ErpSystemRpc erpSystemRpc = new ErpSystemRpc();

// 创建请求对象
InvoiceGoodsPageRequest request = new InvoiceGoodsPageRequest();
request.setCurrentPage(1);
request.setPageSize(10);
request.setItemCode("5560127");

// 调用接口
ErpMultiResponse<InvoiceGoodsResponse> response = erpSystemRpc.getInvoiceGoodsPage(request);

// 处理响应
if (response.isSuccess()) {
    List<InvoiceGoodsResponse> goodsList = response.getData();
    for (InvoiceGoodsResponse goods : goodsList) {
        System.out.println("物料代码: " + goods.getItemCode());
        System.out.println("物料描述: " + goods.getDescription());
        System.out.println("开票描述: " + goods.getInvoiceDesc());
        // ... 处理其他字段
    }
} else {
    System.err.println("接口调用失败: " + response.getMsg());
}
```

## 注意事项

1. 接口使用GET方式请求，所有参数都会作为URL查询参数传递
2. 分页参数currentPage和pageSize为可选参数，如不传递则返回所有数据
3. 查询条件支持模糊匹配
4. 响应中的字段名支持大小写兼容（通过@SerializedName注解实现）
5. 接口需要通过Bearer Token进行身份验证

## 错误处理

当接口调用失败时，可以通过以下方式处理：

```java
if (response.isFailure()) {
    String errorMsg = response.getMsg();
    // 记录错误日志或进行其他错误处理
    log.error("获取物料开票信息失败: {}", errorMsg);
}
```
