package com.xtc.marketing.invoiceservice.rpc.erp;

import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.ErpMultiResponse;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.request.InvoiceGoodsPageRequest;
import com.xtc.marketing.invoiceservice.rpc.erp.erpdto.response.InvoiceGoodsResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * ERP系统RPC测试类
 */
@SpringBootTest
public class ErpSystemRpcTest {

    @Test
    public void testGetInvoiceGoodsPage() {
        ErpSystemRpc rpc = new ErpSystemRpc();
        
        // 创建请求对象
        InvoiceGoodsPageRequest request = new InvoiceGoodsPageRequest();
        request.setCurrentPage(1);
        request.setPageSize(10);
        request.setItemCode("5560127");
        
        // 调用接口
        ErpMultiResponse<InvoiceGoodsResponse> response = rpc.getInvoiceGoodsPage(request);
        
        // 验证响应
        System.out.println("响应状态: " + response.getStatus());
        System.out.println("响应消息: " + response.getMsg());
        System.out.println("总页数: " + response.getTotalPages());
        System.out.println("总记录数: " + response.getTotalRows());
        
        if (response.getData() != null && !response.getData().isEmpty()) {
            System.out.println("第一条记录:");
            InvoiceGoodsResponse firstItem = response.getData().get(0);
            System.out.println("物料代码: " + firstItem.getItemCode());
            System.out.println("物料描述: " + firstItem.getDescription());
            System.out.println("开票描述: " + firstItem.getInvoiceDesc());
            System.out.println("规格型号: " + firstItem.getSpecification());
            System.out.println("默认税率: " + firstItem.getDefaultTaxRate());
        }
    }
}
